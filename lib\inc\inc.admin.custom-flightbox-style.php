<?php
defined('fileaway') or die('Water, water everywhere, but not a drop to drink.');
$output .= 
'/***** YOUR FLIGHTBOX STYLE *****/
div#ssfa-flightbox.ssfa-yourflightboxstyle{
	background: #e2e2e2;
	background: -moz-linear-gradient(45deg, #e2e2e2 0%, #e8e8e8 15%, #ededed 18%, #e0e0e0 21%, #808080 86%, #adadad 93%, #808080 97%, #7a7a7a 100%);
	background: -webkit-gradient(left bottom, right top, color-stop(0%, #e2e2e2), color-stop(15%, #e8e8e8), color-stop(18%, #ededed), color-stop(21%, #e0e0e0), color-stop(86%, #808080), color-stop(93%, #adadad), color-stop(97%, #808080), color-stop(100%, #7a7a7a));
	background: -webkit-linear-gradient(45deg, #e2e2e2 0%, #e8e8e8 15%, #ededed 18%, #e0e0e0 21%, #808080 86%, #adadad 93%, #808080 97%, #7a7a7a 100%);
	background: -o-linear-gradient(45deg, #e2e2e2 0%, #e8e8e8 15%, #ededed 18%, #e0e0e0 21%, #808080 86%, #adadad 93%, #808080 97%, #7a7a7a 100%);
	background: -ms-linear-gradient(45deg, #e2e2e2 0%, #e8e8e8 15%, #ededed 18%, #e0e0e0 21%, #808080 86%, #adadad 93%, #808080 97%, #7a7a7a 100%);
	background: linear-gradient(45deg, #e2e2e2 0%, #e8e8e8 15%, #ededed 18%, #e0e0e0 21%, #808080 86%, #adadad 93%, #808080 97%, #7a7a7a 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#e2e2e2", endColorstr="#7a7a7a", GradientType=1 );
		-moz-box-shadow: 0 10px 250px #222;
		-o-box-shadow: 0 10px 250px #222;	
		-webkit-box-shadow: 0 10px 250px #222;
		box-shadow: 0 10px 250px #222;
		border: 0!important;
}
/***** END YOUR FLIGHTBOX STYLE *****/
';