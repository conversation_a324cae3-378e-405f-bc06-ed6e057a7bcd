/**
 * SoundManager 2 + useFlashBlock
 * ------------------------------
 * Flash positioning and flashblock / clicktoflash handling
 */
#sm2-container {
 /**
  * where the SM2 flash movie goes. by default, relative container.
  * set relative or absolute here, and don't touch it later or bad things will happen (see below comments.)
  */
 position: absolute;
 width: 1px;
 height: 1px;
 overflow: hidden;
 /* screw IE 6, just make it display nice */
 _overflow: hidden;
}
#sm2-container object,
#sm2-container embed {
 /**
  * the actual SWF movie bit.
  * important: The SWF needs to be able to be moved off-screen without display: or position: changes.
  * changing display: or position: or overflow: here or on parent can cause SWF reload or other weird issues after unblock
  * e.g., SM2 starts but strange errors, no whileplaying() etc.
  */
 width: 48px;
 height: 48px;
 /* some flash blockers may also respect this rule */
 max-width: 48px;
 max-height: 48px;
}
#sm2-container.swf_timedout {
 /* expand to show the timed-out SWF content */
 position: relative;
 width: 48px;
 height: 48px;
}
#sm2-container.swf_timedout,
#sm2-container.swf_timedout object,
#sm2-container.swf_timedout embed {
 /**
  * when SM2 didn't start normally, time-out case. flash blocked, missing SWF, no flash?
  * 48px square flash placeholder is typically used by blockers.
  */
 min-width: 48px;
 min-height: 48px;
}
#sm2-container.swf_unblocked {
 /* SWF unblocked, or was never blocked to begin with; try to collapse container as much as possible. */
 width: 1px;
 height: 1px;
}
#sm2-container.swf_loaded object,
#sm2-container.swf_loaded embed,
#sm2-container.swf_unblocked object,
#sm2-container.swf_unblocked embed {
 /* hide flash off-screen (relative to container) when it has loaded OK */
 left: -9999em;
 top: -9999em;
}
#sm2-container.swf_error {
 /* when there is a fatal error (flash loaded, but SM2 failed) */
 display: none;
}
#sm2-container.high_performance,
#sm2-container.high_performance.swf_timeout {
 /* "high performance" case: keep on-screen at all times */
 position: absolute;
 position: fixed;
}
#sm2-container.high_performance {
 overflow: hidden;
 _top: -9999px; /* IE 6 hax, no position:fixed */
 _left: -9999px;
 bottom: 0px;
 left: 0px;
 /**
  * special case: show at first with w/h, hide when unblocked.
  * might be bad/annoying.
  * try to stay within ClickToFlash "invisible" limits (so it won't be blocked.)
  */
 z-index: 99; /* try to stay on top */
}
#sm2-container.high_performance.swf_loaded,
#sm2-container.high_performance.swf_unblocked {
 z-index: auto;
}
#sm2-container.high_performance.swf_loaded,
#sm2-container.high_performance.swf_unblocked,
#sm2-container.high_performance.swf_unblocked object,
#sm2-container.high_performance.swf_unblocked embed {
 /**
  * 8x8px is required minimum to load in fx/win32 in some cases(?)
  * 6x6+ good for fast performance, even better when on-screen via position:fixed
  * also, clickToFlash (Safari <5.1) may auto-load "invisible" SWFs at this size
  */
 height: 8px;
 width: 8px;
}
#sm2-container.high_performance.swf_loaded {
 /* stay bottom/left */
 top: auto;
 bottom: 0px;
 left: 0px;
}
#sm2-container.high_performance.swf_loaded object,
#sm2-container.high_performance.swf_loaded embed,
#sm2-container.high_performance.swf_unblocked object,
#sm2-container.high_performance.swf_unblocked embed {
 /* high-performance case must stay on-screen */
 left: auto;
 top: auto;
}
#sm2-container.high_performance.swf_timedout {
 z-index: 99; /* try to stay on top */
}