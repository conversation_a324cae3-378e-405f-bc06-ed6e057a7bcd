/*!
<PERSON><PERSON>, a Select Box Enhancer for jQ<PERSON>y and Prototype
by <PERSON> for Harvest, http://getharvest.com
Version 1.1.0
Full source at https://github.com/harvesthq/chozed
Copyright (c) 2011 Harvest http://getharvest.com
MIT License, https://github.com/harvesthq/chozed/blob/master/LICENSE.md
This file is generated by `grunt build`, do not edit it by hand.
*/
/* @group Base */
.chozed-container {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  font-size: 13px;
  zoom: 1;
  *display: inline;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.chozed-container .chozed-drop {
  position: absolute;
  top: 100%;
  left: -9999px;
  z-index: 1010;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
  border: 1px solid #aaa;
  border-top: 0;
  background: #fff;
  box-shadow: 0 4px 5px rgba(0, 0, 0, 0.15);
}
.chozed-container.chozed-with-drop .chozed-drop {
  left: 0;
}
.chozed-container a {
  cursor: pointer;
}
/* @end */
/* @group Single Chosen */
.chozed-container-single .chozed-single {
  position: relative;
  display: block;
  overflow: hidden;
  padding: 0 0 0 8px;
  height: 23px;
  border: 1px solid #aaa;
  border-radius: 5px;
  background-color: #fff;
  background: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(20%, #ffffff), color-stop(50%, #f6f6f6), color-stop(52%, #eeeeee), color-stop(100%, #f4f4f4));
  background: -webkit-linear-gradient(top, #ffffff 20%, #f6f6f6 50%, #eeeeee 52%, #f4f4f4 100%);
  background: -moz-linear-gradient(top, #ffffff 20%, #f6f6f6 50%, #eeeeee 52%, #f4f4f4 100%);
  background: -o-linear-gradient(top, #ffffff 20%, #f6f6f6 50%, #eeeeee 52%, #f4f4f4 100%);
  background: linear-gradient(top, #ffffff 20%, #f6f6f6 50%, #eeeeee 52%, #f4f4f4 100%);
  background-clip: padding-box;
  box-shadow: 0 0 3px white inset, 0 1px 1px rgba(0, 0, 0, 0.1);
  color: #444;
  text-decoration: none;
  white-space: nowrap;
  line-height: 24px;
}
.chozed-container-single .chozed-default {
  color: #999;
}
.chozed-container-single .chozed-single span {
  display: block;
  overflow: hidden;
  margin-right: 26px;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.chozed-container-single .chozed-single-with-deselect span {
  margin-right: 38px;
}
.chozed-container-single .chozed-single abbr {
  position: absolute;
  top: 6px;
  right: 26px;
  display: block;
  width: 12px;
  height: 12px;
  background: url('chozed-sprite.png') -42px 1px no-repeat;
  font-size: 1px;
}
.chozed-container-single .chozed-single abbr:hover {
  background-position: -42px -10px;
}
.chozed-container-single.chozed-disabled .chozed-single abbr:hover {
  background-position: -42px -10px;
}
.chozed-container-single .chozed-single div {
  position: absolute;
  top: 0;
  right: 0;
  display: block;
  width: 18px;
  height: 100%;
}
.chozed-container-single .chozed-single div b {
  display: block;
  width: 100%;
  height: 100%;
  background: url('chozed-sprite.png') no-repeat 0px 2px;
}
.chozed-container-single .chozed-search {
  position: relative;
  z-index: 1010;
  margin: 0;
  padding: 3px 4px;
  white-space: nowrap;
}
.chozed-container-single .chozed-search input[type="text"] {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  margin: 1px 0;
  padding: 4px 20px 4px 5px;
  width: 100%;
  height: auto;
  outline: 0;
  border: 1px solid #aaa;
  background: white url('chozed-sprite.png') no-repeat 100% -20px;
  background: url('chozed-sprite.png') no-repeat 100% -20px;
  font-size: 1em;
  font-family: sans-serif;
  line-height: normal;
  border-radius: 0;
}
.chozed-container-single .chozed-drop {
  margin-top: -1px;
  border-radius: 0 0 4px 4px;
  background-clip: padding-box;
}
.chozed-container-single.chozed-container-single-nosearch .chozed-search {
  position: absolute;
  left: -9999px;
}
/* @end */
/* @group Results */
.chozed-container .chozed-results {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  margin: 0 4px 4px 0!important;
  padding: 0 0 0 4px;
  max-height: 240px;
  -webkit-overflow-scrolling: touch;
}
.chozed-container .chozed-results li {
  display: none;
  margin: 0;
  padding: 5px 6px;
  list-style: none;
  line-height: 15px;
  -webkit-touch-callout: none;
}
.chozed-container .chozed-results li.active-result {
  display: list-item;
  cursor: pointer;
}
.chozed-container .chozed-results li.disabled-result {
  display: list-item;
  color: #ccc;
  cursor: default;
}
.chozed-container .chozed-results li.highlighted {
  background-color: #eeeeee;
  background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(20%, #eeeeee), color-stop(90%, #dcdcdc));
  background-image: -webkit-linear-gradient(#eeeeee 20%, #dcdcdc 90%);
  background-image: -moz-linear-gradient(#eeeeee 20%, #dcdcdc 90%);
  background-image: -o-linear-gradient(#eeeeee 20%, #dcdcdc 90%);
  background-image: linear-gradient(#eeeeee 20%, #dcdcdc 90%);
  color: #444;
}
.chozed-container .chozed-results li.no-results {
  display: list-item;
  background: #f4f4f4;
}
.chozed-container .chozed-results li.group-result {
  display: list-item;
  font-weight: bold;
  cursor: default;
}
.chozed-container .chozed-results li.group-option {
  padding-left: 15px;
}
.chozed-container .chozed-results li em {
  font-style: normal;
  text-decoration: underline;
}
/* @end */
/* @group Multi Chosen */
.chozed-container-multi .chozed-choices {
  position: relative;
  overflow: hidden;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  width: 100%;
  height: auto !important;
  height: 1%;
  border: 1px solid #aaa;
  background-color: #fff;
  background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(1%, #eeeeee), color-stop(15%, #ffffff));
  background-image: -webkit-linear-gradient(#eeeeee 1%, #ffffff 15%);
  background-image: -moz-linear-gradient(#eeeeee 1%, #ffffff 15%);
  background-image: -o-linear-gradient(#eeeeee 1%, #ffffff 15%);
  background-image: linear-gradient(#eeeeee 1%, #ffffff 15%);
  cursor: text;
}
.chozed-container-multi .chozed-choices li {
  float: left;
  list-style: none;
}
.chozed-container-multi .chozed-choices li.search-field {
  margin: 0;
  padding: 0;
  white-space: nowrap;
}
.chozed-container-multi .chozed-choices li.search-field input[type="text"] {
  margin: 1px 0;
  padding: 5px;
  height: 15px;
  outline: 0;
  border: 0 !important;
  background: transparent !important;
  box-shadow: none;
  color: #666;
  font-size: 100%;
  font-family: sans-serif;
  line-height: normal;
  border-radius: 0;
}
.chozed-container-multi .chozed-choices li.search-field .default {
  color: #999;
}
.chozed-container-multi .chozed-choices li.search-choice {
  position: relative;
  margin: 3px 0 3px 5px;
  padding: 3px 20px 3px 5px;
  border: 1px solid #aaa;
  border-radius: 3px;
  background-color: #e4e4e4;
  background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(20%, #f4f4f4), color-stop(50%, #f0f0f0), color-stop(52%, #e8e8e8), color-stop(100%, #eeeeee));
  background-image: -webkit-linear-gradient(#f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  background-image: -moz-linear-gradient(#f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  background-image: -o-linear-gradient(#f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  background-image: linear-gradient(#f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  background-clip: padding-box;
  box-shadow: 0 0 2px white inset, 0 1px 0 rgba(0, 0, 0, 0.05);
  color: #333;
  line-height: 13px;
  cursor: default;
}
.chozed-container-multi .chozed-choices li.search-choice .search-choice-close {
  position: absolute;
  top: 4px;
  right: 3px;
  display: block;
  width: 12px;
  height: 12px;
  background: url('chozed-sprite.png') -42px 1px no-repeat;
  font-size: 1px;
}
.chozed-container-multi .chozed-choices li.search-choice .search-choice-close:hover {
  background-position: -42px -10px;
}
.chozed-container-multi .chozed-choices li.search-choice-disabled {
  padding-right: 5px;
  border: 1px solid #ccc;
  background-color: #e4e4e4;
  background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(20%, #f4f4f4), color-stop(50%, #f0f0f0), color-stop(52%, #e8e8e8), color-stop(100%, #eeeeee));
  background-image: -webkit-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  background-image: -moz-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  background-image: -o-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  background-image: linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  color: #666;
}
.chozed-container-multi .chozed-choices li.search-choice-focus {
  background: #d4d4d4;
}
.chozed-container-multi .chozed-choices li.search-choice-focus .search-choice-close {
  background-position: -42px -10px;
}
.chozed-container-multi .chozed-results {
  margin: 0;
  padding: 0;
}
.chozed-container-multi .chozed-drop .result-selected {
  display: list-item;
  color: #ccc;
  cursor: default;
}
/* @end */
/* @group Active  */
.chozed-container-active .chozed-single {
  border: 1px solid #5897fb;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}
.chozed-container-active.chozed-with-drop .chozed-single {
  border: 1px solid #aaa;
  -moz-border-radius-bottomright: 0;
  border-bottom-right-radius: 0;
  -moz-border-radius-bottomleft: 0;
  border-bottom-left-radius: 0;
  background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(20%, #eeeeee), color-stop(80%, #ffffff));
  background-image: -webkit-linear-gradient(#eeeeee 20%, #ffffff 80%);
  background-image: -moz-linear-gradient(#eeeeee 20%, #ffffff 80%);
  background-image: -o-linear-gradient(#eeeeee 20%, #ffffff 80%);
  background-image: linear-gradient(#eeeeee 20%, #ffffff 80%);
  box-shadow: 0 1px 0 #fff inset;
}
.chozed-container-active.chozed-with-drop .chozed-single div {
  border-left: none;
  background: transparent;
}
.chozed-container-active.chozed-with-drop .chozed-single div b {
  background-position: -18px 2px;
}
.chozed-container-active .chozed-choices {
  border: 1px solid #5897fb;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}
.chozed-container-active .chozed-choices li.search-field input[type="text"] {
  color: #111 !important;
}
/* @end */
/* @group Disabled Support */
.chozed-disabled {
  opacity: 0.5 !important;
  cursor: default;
}
.chozed-disabled .chozed-single {
  cursor: default;
}
.chozed-disabled .chozed-choices .search-choice .search-choice-close {
  cursor: default;
}
/* @end */
/* @group Right to Left */
.chozed-rtl {
  text-align: right;
}
.chozed-rtl .chozed-single {
  overflow: visible;
  padding: 0 8px 0 0;
}
.chozed-rtl .chozed-single span {
  margin-right: 0;
  margin-left: 26px;
  direction: rtl;
}
.chozed-rtl .chozed-single-with-deselect span {
  margin-left: 38px;
}
.chozed-rtl .chozed-single div {
  right: auto;
  left: 3px;
}
.chozed-rtl .chozed-single abbr {
  right: auto;
  left: 26px;
}
.chozed-rtl .chozed-choices li {
  float: right;
}
.chozed-rtl .chozed-choices li.search-field input[type="text"] {
  direction: rtl;
}
.chozed-rtl .chozed-choices li.search-choice {
  margin: 3px 5px 3px 0;
  padding: 3px 5px 3px 19px;
}
.chozed-rtl .chozed-choices li.search-choice .search-choice-close {
  right: auto;
  left: 4px;
}
.chozed-rtl.chozed-container-single-nosearch .chozed-search,
.chozed-rtl .chozed-drop {
  left: 9999px;
}
.chozed-rtl.chozed-container-single .chozed-results {
  margin: 0 0 4px 4px;
  padding: 0 4px 0 0;
}
.chozed-rtl .chozed-results li.group-option {
  padding-right: 15px;
  padding-left: 0;
}
.chozed-rtl.chozed-container-active.chozed-with-drop .chozed-single div {
  border-right: none;
}
.chozed-rtl .chozed-search input[type="text"] {
  padding: 4px 5px 4px 20px;
  background: white url('chozed-sprite.png') no-repeat -30px -20px;
  background: url('chozed-sprite.png') no-repeat -30px -20px;
  direction: rtl;
}
.chozed-rtl.chozed-container-single .chozed-single div b {
  background-position: 6px 2px;
}
.chozed-rtl.chozed-container-single.chozed-with-drop .chozed-single div b {
  background-position: -12px 2px;
}
/* @end */
/* @group Retina compatibility */
@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min-resolution: 144dpi) {
  .chozed-rtl .chozed-search input[type="text"],
  .chozed-container-single .chozed-single abbr,
  .chozed-container-single .chozed-single div b,
  .chozed-container-single .chozed-search input[type="text"],
  .chozed-container-multi .chozed-choices .search-choice .search-choice-close,
  .chozed-container .chozed-results-scroll-down span,
  .chozed-container .chozed-results-scroll-up span {
    background-image: url('<EMAIL>') !important;
    background-size: 52px 37px !important;
    background-repeat: no-repeat !important;
  }
}
/* @end */