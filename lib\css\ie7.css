.ssfa-fileaplay-arrow-down-alt1 {
	*zoom: expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#x21;');
}
.ssfa-fileaplay-arrow-down-alt2 {
	*zoom: expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#x22;');
}
.ssfa-fileaplay-play {
	*zoom: expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#x23;');
}
.ssfa-fileaplay-pause {
	*zoom: expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#x24;');
}
.ssfa-fileaplay-download {
	*zoom: expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#x25;');
}
.ssfa-fileaplay-play2 {
	*zoom: expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#x26;');
}
.ssfa-fileaplay-pause2 {
	*zoom: expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#x27;');
}
.ssfa-fileaplay-box-add {
	*zoom: expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#x28;');
}
.ssfa-fileaplay-download2 {
	*zoom: expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#x29;');
}
.ssfa-fileaplay-play3 {
	*zoom: expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#x2a;');
}
.ssfa-fileaplay-pause3 {
	*zoom: expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#x2b;');
}
.ssfa-fileaplay-play22 {
	*zoom: expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#x2c;');
}
.ssfa-fileaplay-pause22 {
	*zoom: expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#x2d;');
}
.ssfa-fileaplay-in {
	*zoom: expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#x2e;');
}
.ssfa-fileaplay-play4 {
	*zoom: expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#x2f;');
}
.ssfa-fileaplay-pause4 {
	*zoom: expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#x30;');
}
.ssfa-fileaplay-play32 {
	*zoom: expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#x31;');
}
.ssfa-fileaplay-pause32 {
	*zoom: expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#x32;');
}