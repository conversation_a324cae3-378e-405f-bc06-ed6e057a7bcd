#fileawaymodal-form { font-family: 'Trebuchet MS'; color: #444444; }
div#TB_ajaxContent{width:96%!important;}
div.chozed-container{margin-bottom:0!important;}
div.fileaway-customcss{
	margin-bottom:15px!important;
}
div.fileaway-first-inline{
/*	float:left;
	clear:left!important;
	margin: 0 15px 10px 0; */
	display:inline-block;
	width:30%;
}
div.fileaway-inline{
/*	float:left;
	clear:none; */
	display:inline-block;
	margin: 0 15px 10px 0;
	width:30%;	
	vertical-align:top;
}
div.fileaway-half{
	width:14%!important;	
}
.fileaway-help-content {
    background-color: #FFFFFF;
    border: 2px solid #000000;
    box-shadow: 0 2px 8px #000000;
    margin: auto;
    padding: 25px;
    position: relative;
    top: 25%;
    width: 70%;
	-moz-border-radius: 0;
	-webkit-border-radius: 0;
	border-radius: 0;
	border: 3px solid #8e8983;
	font-family: 'Trebuchet MS'; 
	color: #444444;	
}
.fileaway-help-content > h4 {
	margin: 0 0 15px;
	font-size: 26px;
	font-family: 'Yanone Light', 'Open Sans', Helvetica, 'Lucida Sans Unicode', sans-serif;
	font-weight: bold;
	text-transform: uppercase;
	color: #6C6762;
}
.fileaway-help-content code {
	background: #E1DCD3 !important;
	font-size: 11px!important;
}
.fileaway-help-backdrop {
	position:fixed; 
	top: 0;
	left: 0;
	background-color: rgba(0, 0, 0, 0.7);
	width:100%;
	height:100%;	
	display:none;
	z-index: 99999999999999 !important;
}
.fileaway-help-close {
	cursor:pointer;
	position: absolute;
	right: 10px;
	top: 10px;
	font-size: 20px;
	color: #69645F;
}
.fileaway-help-close:hover {
	color: #8E8983;
}
.fileaway-helplink {
	position: relative;
	top: 0; 
	cursor: pointer;
	font-size:12px;
	right: 5px;
	color: #A6A29E;	
}
#fileawaymodal-form input[type="text"], 
#fileawaymodal-form input[type="password"], 
#fileawaymodal-form input[type="file"], 
#fileawaymodal-form input[type="email"], 
#fileawaymodal-form input[type="number"], 
#fileawaymodal-form input[type="search"], 
#fileawaymodal-form input[type="tel"], 
#fileawaymodal-form input[type="url"]
{ 
	background-color: #f8f6f2!important; 
	display: inline-block;
	border: 1px solid #DFDFDF;
	border-radius: 3px;
	padding: 0 3px;
	height: 20.5px;
	box-shadow:0!important;
	font-size:13px;
	width:100%;
}
#fileawaymodal-form input[type="text"]:focus, 
#fileawaymodal-form input[type="password"]:focus, 
#fileawaymodal-form input[type="file"]:focus, 
#fileawaymodal-form input[type="email"]:focus, 
#fileawaymodal-form input[type="number"]:focus, 
#fileawaymodal-form input[type="search"]:focus, 
#fileawaymodal-form input[type="tel"]:focus, 
#fileawaymodal-form input[type="url"]:focus
{
	background-color: #f8f6f2;
	display: inline-block;
	border-radius: 3px;
	padding: 0 3px;
	height: 20.5px;
	border: 1px solid #AAAAAA;
	box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);	
	width:100%;
}
.chozed-container-multi .chozed-choices li.search-field input[type="text"],
.chozed-container-multi .chozed-choices li.search-field input[type="text"]:focus
{
	box-shadow: none!important;
	border: 0!important;	
}
::-webkit-input-placeholder{ 
   color: #BBBBBB;  
}
:-moz-placeholder{
   color: #BBBBBB;  
}
::-moz-placeholder{
   color: #BBBBBB;  
}
:-ms-input-placeholder{  
   color: #BBBBBB;  
}
.chozed-single{
	border:  1px solid #DFDFDF!important;
	-webkit-box-shadow: 0 0 3px #ffffff inset, 0 1px 1px rgba(0,0,0,0.1)!important;
	-moz-box-shadow: 0 0 3px #ffffff inset, 0 1px 1px rgba(0,0,0,0.1)!important;
	box-shadow: 0 0 3px #ffffff inset, 0 1px 1px rgba(0,0,0,0.1)!important;
	border-radius: 3px!important;
}
.chozed-container-active{
	border:  0!important;
}
@font-face {
    font-family: 'Yanone Light';
    src: url('fonts/yanonekaffeesatz-light-webfont.eot');
    src: url('fonts/yanonekaffeesatz-light-webfont.eot?#iefix') format('embedded-opentype'),
         url('fonts/yanonekaffeesatz-light-webfont.woff') format('woff'),
         url('fonts/yanonekaffeesatz-light-webfont.ttf') format('truetype'),
         url('fonts/yanonekaffeesatz-light-webfont.svg#yanone_kaffeesatzlight') format('svg');
    font-weight: normal;
    font-style: normal;
}
#fileawaymodal-form  a:focus, #fileawaymodal-form  a:active { outline: none !important; }
.fileaway-help-content kbd, .fileaway-help-content code 
{
	background: #E1DCD3 !important;
}
div.fileaway-tabs-panel label, .accordion dt label 
{
	font-family: 'Yanone Light', 'Trebuchet MS', sans-serif;
}
div.fileaway-tabs-panel label {	font-size: 15px; color: #666666;}
.fileaway-tabs-panel h4 {
    color: #666666;
    font-family: "Yanone Light",'Helvetica','Open Sans','Trebuchet MS',sans-serif;
    font-size: 1.6em;
    font-style: italic;
    font-weight: normal;
    margin: 5px 0;
	text-transform: none;
}
.fileaway-tabs-nav, .tabs, .tabs-nav li {
	border-bottom: 1px solid #cfcac5!important;
	top: 1px!important;	
}
.fileaway-tab-state-active{
	background: none !important;
	border: 0!important;
}
.fileaway-tabs-nav{
	height: 27px!important;
	margin: 20px 0!important;
	padding: 2px 0!important;
}
.fileaway-tabs-nav li {
	display: block!important;
	float: left!important;
	margin: 0!important;
}
.fileaway-tabs-nav li a {
	font-weight: bold!important;
	border-style: solid!important;
	border-color: #cfcac5!important;
	border-width: 1px 1px 0!important;
	color: #6c6762!important;
	text-shadow: none!important;
	display: inline-block!important;
	padding:2px 15px 4px!important;
	text-decoration: none!important;
	margin: 3.5px -11px -1px 10px!important;
	-moz-border-radius: 0!important;
	-webkit-border-radius: 0!important;
	-o-border-radius: 0!important;
	border-radius: 0!important;
	transition: all .5s ease-out!important;	
	-moz-transition: all .5s ease-out!important;	
	-o-transition: all .5s ease-out!important;	
	-webkit-transition: all .5s ease-out!important;				
}
.fileaway-tabs-nav a {
background: #f3f0eb!important;
}
.fileaway-tabs-nav a:hover,
.fileaway-tabs-nav a:focus,
.fileaway-tabs-nav a:active
 {
	background:#FFFFFF!important;
	color:#4a7f96!important;
	border-color: #cfcac5!important;
	outline:none!important;
	box-shadow: none!important;
 }
ul.fileaway-bulletlist {
	list-style:disc outside none;
	padding: 0 0 30px 30px;
}
ul.fileaway-bulletlist li {
	margin-bottom: 0;
}
.fileaway-tabs-nav li.tabs-selected a,
.fileaway-tabs-nav li.state-active a {
background:#FFFFFF!important;
border-color: #cfcac5!important;
color:#4a7f96!important;
cursor:default!important;
padding: 5px 15px 4px!important;
margin-top: 0px!important;
margin-right: -10.5px!important;
}
.fileaway-tabs-panel {
	clear: both!important;
}
.fileaway-tabs-panel h3 {
	font-size: 36px!important;
	font-family: 'Yanone Light','Helvetica','Open Sans','Trebuchet MS',sans-serif!important;
	color: #6c6762!important;
	text-transform: uppercase!important;
	margin: 30px 0!important;
	padding: 0 0 5px!important;
	line-height: 35px!important;
	text-shadow: 0 1px 0 #fff!important;
	letter-spacing: 3px!important;
}
.fileaway-tabs, .tabs {
	padding: 0!important;	
}
table.fileaway-radio td {
	padding: 0 !important;
}
div.fileaway-description {
	font-size:11px;
	color:#666;
}
#toppathwrap { position:fixed; top:0px; right:0px; background-color:#F2F1E8; padding:5px; display:none; }
.fileaway-selectIt {
    -moz-box-shadow: 0px 1px 3px 0px #1c1b18;
    -webkit-box-shadow: 0px 1px 3px 0px #1c1b18;
    box-shadow: 0px 1px 3px 0px #1c1b18;
    background:-webkit-gradient(linear, left top, left bottom, color-stop(0.05, #d5d2d0), color-stop(1, #cfcac5));
    background:-moz-linear-gradient(top, #d5d2d0 5%, #cfcac5 100%);
    background:-webkit-linear-gradient(top, #d5d2d0 5%, #cfcac5 100%);
    background:-o-linear-gradient(top, #d5d2d0 5%, #cfcac5 100%);
    background:-ms-linear-gradient(top, #d5d2d0 5%, #cfcac5 100%);
    background:linear-gradient(to bottom, #d5d2d0 5%, #cfcac5 100%);
    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr="#d5d2d0", endColorstr="#cfcac5",GradientType=0); 
    background-color:#d5d2d0;
    -moz-border-radius:3px;
    -webkit-border-radius:3px;
    border-radius:3px;       
    border:1px solid #777777;        
    color:#444444;
    font-family:Trebuchet MS;
    font-weight:normal;
    text-decoration:none;
}
.fileaway-selectIt:hover{       
    background:-webkit-gradient(linear, left top, left bottom, color-stop(0.05, #cfcac5), color-stop(1, #d5d2d0));
    background:-moz-linear-gradient(top, #cfcac5 5%, #d5d2d0 100%);
    background:-webkit-linear-gradient(top, #cfcac5 5%, #d5d2d0 100%);
    background:-o-linear-gradient(top, #cfcac5 5%, #d5d2d0 100%);
    background:-ms-linear-gradient(top, #cfcac5 5%, #d5d2d0 100%);
    background:linear-gradient(to bottom, #cfcac5 5%, #d5d2d0 100%);
    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr="#cfcac5", endColorstr="#d5d2d0",GradientType=0);    
    background-color:#cfcac5;
	cursor:pointer;
    color:#444444!important;
}
.fileaway-selectIt:focus, .fileaway-selectIt:active {
	outline:none!important;
    color:#444444!important;	
}
.fileaway-selectIt{
	padding: 1px 20px!important;
	margin: 0!important;
	position: absolute!important;
	right: 20px!important;
	top: 110px!important;
	font-size: 12px!important;
}
div.fileaway-file-away h3 {
	font-size: 36px;
	font-family: 'Yanone Light','Helvetica','Open Sans','Trebuchet MS',sans-serif;
	color: #6c6762;
	text-transform: uppercase;
	margin: 0;
	padding: 0 0 5px;
	line-height: 35px;
	text-shadow: 0 1px 0 #fff;
	letter-spacing: 3px;
}
ul.fileaway-bulletlist {
	list-style:disc outside none;
	padding: 0 0 30px 30px;
}
ul.fileaway-bulletlist li {
	margin-bottom: 0;
}
@font-face {
  font-family: 'fileaway-search';
  src: url('fonts/fileaway-search.eot');
  src: url('fonts/fileaway-search.eot') format('embedded-opentype'), 
	   url('fonts/fileaway-search.woff') format('woff'), 
	   url('fonts/fileaway-search.ttf') format('truetype'), 
	   url('fonts/fileaway-search.svg') format('svg');
  font-style: normal;
  font-weight: normal;
}
@font-face {
	font-family: 'fileaway-search';
	src: url(data:application/x-font-ttf;charset=utf-8;base64,AAEAAAALAIAAAwAwT1MvMg6v8yoAAAC8AAAAYGNtYXDL8RqdAAABHAAAADxnYXNwAAAAEAAAAVgAAAAIZ2x5Zt57LpYAAAFgAAAA2GhlYWQANZaNAAACOAAAADZoaGVhB6oD3AAAAnAAAAAkaG10eAYAAAAAAAKUAAAADGxvY2EACgBsAAACoAAAAAhtYXhwAAYARwAAAqgAAAAgbmFtZaEkpIcAAALIAAABXXBvc3QAAwAAAAAEKAAAACAAAwQAAZAABQAAApkCzAAAAI8CmQLMAAAB6wAzAQkAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAACDmAAPA/8D/wAPAAEAAAAAAAAAAAAAAAAAAAAAgAAAAAAACAAAAAwAAABQAAwABAAAAFAAEACgAAAAGAAQAAQACACDmAP//AAAAIOYA////4RoCAAEAAAAAAAAAAQAB//8ADwABAAAAAAAAAAAAAgAANzkBAAAAAAIAAP/YA+gDwAAvAEQAACUnLgMjPgM1NC4CIyIOAhUUHgIzMj4CNxQeAh8BHgI2Nz4BLgEnJSIuAjU0PgIzMh4CFRQOAiMD4PMJExMSCBUiGA08aIxQUIxoPDxojFAkRD85GQQIDAjODSEjIQ0NDAIQD/2gNV1FKChFXTU1XUUoKEVdNVnOCAwIBBk5P0QkUIxoPDxojFBQjGg8DRgiFQgSExMJ8w8QAgwNDSEjIQ3nKEVdNTVdRSgoRV01NV1FKAAAAAABAAAAAQAAbc8mAF8PPPUACwQAAAAAAM6XKPwAAAAAzpco/AAA/9gD6APAAAAACAACAAAAAAAAAAEAAAPA/8AAAAQAAAAAGAPoAAEAAAAAAAAAAAAAAAAAAAADAAAAAAIAAAAEAAAAAAAAAAAKAGwAAQAAAAMARQACAAAAAAACAAAAAAAAAAAAAAAAAAAAAAAAAA4ArgABAAAAAAABABYAAAABAAAAAAACAA4AYwABAAAAAAADABYALAABAAAAAAAEABYAcQABAAAAAAAFABYAFgABAAAAAAAGAAsAQgABAAAAAAAKACgAhwADAAEECQABABYAAAADAAEECQACAA4AYwADAAEECQADABYALAADAAEECQAEABYAcQADAAEECQAFABYAFgADAAEECQAGABYATQADAAEECQAKACgAhwBzAHMAZgBhAC0AcwBlAGEAcgBjAGgAVgBlAHIAcwBpAG8AbgAgADAALgAwAHMAcwBmAGEALQBzAGUAYQByAGMAaHNzZmEtc2VhcmNoAHMAcwBmAGEALQBzAGUAYQByAGMAaABSAGUAZwB1AGwAYQByAHMAcwBmAGEALQBzAGUAYQByAGMAaABHAGUAbgBlAHIAYQB0AGUAZAAgAGIAeQAgAEkAYwBvAE0AbwBvAG4AAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==) format('truetype'),
		 url(data:application/font-woff;charset=utf-8;base64,d09GRgABAAAAAASUAAsAAAAABEgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABPUy8yAAABCAAAAGAAAABgDq/zKmNtYXAAAAFoAAAAPAAAADzL8RqdZ2FzcAAAAaQAAAAIAAAACAAAABBnbHlmAAABrAAAANgAAADY3nsulmhlYWQAAAKEAAAANgAAADYANZaNaGhlYQAAArwAAAAkAAAAJAeqA9xobXR4AAAC4AAAAAwAAAAMBgAAAGxvY2EAAALsAAAACAAAAAgACgBsbWF4cAAAAvQAAAAgAAAAIAAGAEduYW1lAAADFAAAAV0AAAFdoSSkh3Bvc3QAAAR0AAAAIAAAACAAAwAAAAMEAAGQAAUAAAKZAswAAACPApkCzAAAAesAMwEJAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAg5gADwP/A/8ADwABAAAAAAAAAAAAAAAAAAAAAIAAAAAAAAgAAAAMAAAAUAAMAAQAAABQABAAoAAAABgAEAAEAAgAg5gD//wAAACDmAP///+EaAgABAAAAAAAAAAEAAf//AA8AAQAAAAAAAAAAAAIAADc5AQAAAAACAAD/2APoA8AALwBEAAAlJy4DIz4DNTQuAiMiDgIVFB4CMzI+AjcUHgIfAR4CNjc+AS4BJyUiLgI1ND4CMzIeAhUUDgIjA+DzCRMTEggVIhgNPGiMUFCMaDw8aIxQJEQ/ORkECAwIzg0hIyENDQwCEA/9oDVdRSgoRV01NV1FKChFXTVZzggMCAQZOT9EJFCMaDw8aIxQUIxoPA0YIhUIEhMTCfMPEAIMDQ0hIyEN5yhFXTU1XUUoKEVdNTVdRSgAAAAAAQAAAAEAAG3PJgBfDzz1AAsEAAAAAADOlyj8AAAAAM6XKPwAAP/YA+gDwAAAAAgAAgAAAAAAAAABAAADwP/AAAAEAAAAABgD6AABAAAAAAAAAAAAAAAAAAAAAwAAAAACAAAABAAAAAAAAAAACgBsAAEAAAADAEUAAgAAAAAAAgAAAAAAAAAAAAAAAAAAAAAAAAAOAK4AAQAAAAAAAQAWAAAAAQAAAAAAAgAOAGMAAQAAAAAAAwAWACwAAQAAAAAABAAWAHEAAQAAAAAABQAWABYAAQAAAAAABgALAEIAAQAAAAAACgAoAIcAAwABBAkAAQAWAAAAAwABBAkAAgAOAGMAAwABBAkAAwAWACwAAwABBAkABAAWAHEAAwABBAkABQAWABYAAwABBAkABgAWAE0AAwABBAkACgAoAIcAcwBzAGYAYQAtAHMAZQBhAHIAYwBoAFYAZQByAHMAaQBvAG4AIAAwAC4AMABzAHMAZgBhAC0AcwBlAGEAcgBjAGhzc2ZhLXNlYXJjaABzAHMAZgBhAC0AcwBlAGEAcgBjAGgAUgBlAGcAdQBsAGEAcgBzAHMAZgBhAC0AcwBlAGEAcgBjAGgARwBlAG4AZQByAGEAdABlAGQAIABiAHkAIABJAGMAbwBNAG8AbwBuAAAAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=) format('woff');
	font-weight: normal;
	font-style: normal;
}
[class*="fileaway-icon-"] {
	font-family: 'fileaway-search';
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	/* Better Font Rendering =========== */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
.fileaway-icon-search:before {
	content: "\e600";
}
@font-face {
	font-family: 'File Away Help';
	src: url('fonts/fileaway-help.eot');
	src: url('fonts/fileaway-help.eot') format('embedded-opentype'), 
	url('fonts/fileaway-help.woff') format('woff'), 
	url('fonts/fileaway-help.ttf') format('truetype'), 
	url('fonts/fileaway-help.svg') format('svg');
  font-style: normal;
  font-weight: normal;
}
@font-face {
	font-family: 'File Away Help';
	src: url(data:application/x-font-ttf;charset=utf-8;base64,AAEAAAALAIAAAwAwT1MvMg6v8zMAAAC8AAAAYGNtYXDL+hqdAAABHAAAADxnYXNwAAAAEAAAAVgAAAAIZ2x5ZqYG1iQAAAFgAAAI0GhlYWQAVqI6AAAKMAAAADZoaGVhB8IDzQAACmgAAAAkaG10eCoAAycAAAqMAAAAMGxvY2EMvgo4AAAKvAAAABptYXhwABEAhwAACtgAAAAgbmFtZWHaWwgAAAr4AAABeHBvc3QAAwAAAAAMcAAAACAAAwQAAZAABQAAApkCzAAAAI8CmQLMAAAB6wAzAQkAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAACDmCQPA/8D/wAPAAEAAAAAAAAAAAAAAAAAAAAAgAAAAAAACAAAAAwAAABQAAwABAAAAFAAEACgAAAAGAAQAAQACACDmCf//AAAAIOYA////4RoCAAEAAAAAAAAAAQAB//8ADwABAAAAAAAAAAAAAgAANzkBAAAAAAQAAP/ABAADwAAUACkALQA3AAABIg4CFRQeAjMyPgI1NC4CIxEiLgI1ND4CMzIeAhUUDgIjAzMVIxMhNTM1IzUzETMCAGq7i1BQi7tqaruLUFCLu2pWmHFBQXGYVlaYcUFBcZhWQICAwP8AQEDAQAPAUIu7amq7i1BQi7tqaruLUPxgQXGYVlaYcUFBcZhWVphxQQKggP6AQMBA/wAAAAADAAD/wAQAA8AAFAAZACQAAAEiDgIVFB4CMzI+AjU0LgIjBzMVIzUTITUzESM1MxEzFQIAaruLUFCLu2pqu4tQUIu7akCAgMD/AEBAwEADwFCLu2pqu4tQUIu7amq7i1DAgID9gEABAED+wEAAAAAAAQAC/8ID/gO+AIQAACU4AzEJATgDMT4DNz4BLgEvAS4CBgcOAwc4AzEJATgDMS4DJy4BDgEPAQ4CFhceAxc4AzEJATgDMQ4DBw4BHgEfAR4CNjc+Azc4AzEJATgDMR4DFx4BPgE/AT4CJicuAycD9/7JATcBAgIBAQIBAgQDkwMICQkEAgMDAwH+yf7JAQMDAwIECQkIA5MDBAIBAgEBAgIBATf+yQECAgEBAgECBAOTAwgJCQQCAwMDAQE3ATcBAwMDAgQJCQgDkwMEAgECAQECAgGJATcBNwEDAwMCBAkJCAOTAwQCAQIBAQICAf7JATcBAgIBAQIBAgQDkwMICQkEAgMDAwH+yf7JAQMDAwIECQkIA5MDBAIBAgEBAgIBATf+yQECAgEBAgECBAOTAwgJCQQCAwMDAQAAAAQAAP/ABAADwAAUACkAVgBbAAAFIi4CNTQ+AjMyHgIVFA4CIxEiDgIVFB4CMzI+AjU0LgIjFSIOAhUzND4CMzIeAhUUDgIjKgMjIg4CHQEzNT4DNTQuAiMRIxUzNQIAaruLUFCLu2pqu4tQUIu7alCMaDw8aIxQUIxoPDxojFAoRjQegAoRFw0NFxEKChEXDQEEBwsIBwwJBYAcLyITHjRGKECAQFCLu2pqu4tQUIu7amq7i1ADgDxojFBQjGg8PGiMUFCMaDyAHjRGKA0XEQoKERcNDRcRCgUJDAdgDAojLzkfKEY0Hv5AQEAAAwCWAFYDagMqABQAKQA2AAA3LgE0Njc+ATIWFx4BFAYHDgEiJicBLgEiBgcOARQWFx4BMjY3PgE0JicBJzcnNxc3FwcXBycHlktLS0tLvcW9S0tLS0tLvcW9SwJ6OI6Tjjg4ODg4OI6Tjjg4ODg4/mlbiIhbiIhaiIhaiIhWS73FvUtLS0tLS73FvUtLS0tLAno4ODg4OI6Tjjg4ODg4OI6Tjjj+DluIiFuIiFuIiFuIiAAAAwAp//YD1wOkABQAKwCDAAABDgMXHgM3PgMnLgMHAyMiLgI1ND4COwEyHgIVFA4CIxMOAw8BDgMHDgMdASM1ND4CNz4DNz4DNz4DNTQuAicuAyMiDgIHDgMdASM1PgM3PgMzMh4CFx4DFRQOAgcB+mKrfkgBAUyCrWJiq35IAQFMgq1iAQMPGRIKCxIZDwIPGRIKCxIZD6gECw4RCh0GCQcFAQECAQFuAQQHBgkbGhMBAwUFBAIEBgQCAgQGBAQKDhEKChEOCwQEBgQCcQEKEhoRCxgbHRAVJiIfDg4VDgcDBwoHA6QBTIKtYmGrfkgBAUyCrWJiq35IAf0KCxMZDw8ZEgoLExoPDxgRCgFOBQsNDggUBQkJCQQDBQYIBwUJDhcTEAcLFxMNAQIFBQUDBgsKCQUGDAwLBQUIBQMDBgoGBw0ODgcFBRotJR4LBwoHBAUKDwoKGR4iEwsVFBQJAAIAUgAfA64DewAUACEAAAEiDgIVFB4CMzI+AjU0LgIjExcHJwcnNyc3FzcXBwIAWZ11RER1nVlZnXVERHWdWVmdWZ2dWJ2dWJ2dWZ0De0R1nVlZnXVERHWdWVmddUT+Up1ZnZ1ZnZ1ZnZ1YnQAAAAADACn/9gPXA6QAFAApAFYAAAEOAxceAzc+AycuAwcXMh4CFRQOAiMiLgI1ND4CMwMiLgE2PwE+ATQmIyIOAgcnPgMzMh4BBg8BDgEeATMyPgI3Fw4DIwH6Yqt+SAEBTIKtYmKrfkgBAUyCrWI0EhgOBgwWIBQRGRAIChYiGHsMFAsBCSYCAwMDBBIWGAkQHj45Lw8MDgUEBisDAgEEAwQPFBcMEh07NCoMA6QBTIKtYmGrfkgBAUyCrWJiq35IAZ0KEBYMDxwVDQgQFg4MGxYP/aAJGCohmwkNCAQGCgwGGxknGw4OGiQWowsPCQQECQ4JGR0pGgwAAAIBFP/NAusDzQAUAEEAAAEyHgIVFA4CIyIuAjc0PgIzAyIuATY3Ez4BNCYjIg4CByc+AzMyHgEGBwMOAR4BMzI+AjcXDgMjAn0aJxoNFSUyHRgmGg0BESIzItMUIRIBDj4EBQQFBh4lJxAbMWdeThgUGAgGCkcFAwIGBQYZISYUHzBhV0YUA80QHCQUGS4kFQ4aJhgULSYZ/AAPKEY3AQUQFg4HChAVCy0qQi4YFys9Jv7uEhkPBwgPFxAqMUUrFAAAAAACANcAAAMpA5oAYQB2AAABLgMjIg4CBw4DBzM0PgI3PgMzMh4CFx4DFRQOAgcOAwcwDgIHDgMHFB4CMzoDMzI+AjU0PgI3PgM/AT4DNz4DNTQuAicDJg4CBwYeAhcWPgI3Ni4CJwLRFjE3PSEZLiomERsqHRACqQQHCwcHExgdEREdGBIGBwoGAwUICgYDBwgJBR4qLQ8JCgYCAQEDBgYGJywmBQUGBAEBAgIBAwoNEAgvEBwXEgYJEAsGCxYhFt8WJx0SAQEPHCYWFycdEQEBDxwnFwNaEBgQCAYLEQsRLztHKgwYFxcLCxELBgUJDgkJExQVCwoSEhEIBQkJCAQUHyYSChogJhYCBAQDAwQFAggNCgkFCRAPDgcgDBYUEggNHSAjEx82LycQ/XYBDhsmGBgnHBABAQ0aJhgYJxwQAQAAAAABAAAAAQAA+0n3OV8PPPUACwQAAAAAAM6brtgAAAAAzpuu2AAA/8AEAAPNAAAACAACAAAAAAAAAAEAAAPA/8AAAAQAAAAAAAQAAAEAAAAAAAAAAAAAAAAAAAAMAAAAAAIAAAAEAAAABAAAAAQAAAIEAAAABAAAlgQAACkEAABSBAAAKQQAARQEAADXAAAAAAAKAFoAkgEwAaYCAAKwAugDZAPGBGgAAAABAAAADACFAAQAAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAAADgCuAAEAAAAAAAEAHAAAAAEAAAAAAAIADgB4AAEAAAAAAAMAHAAyAAEAAAAAAAQAHACGAAEAAAAAAAUAFgAcAAEAAAAAAAYADgBOAAEAAAAAAAoAKACiAAMAAQQJAAEAHAAAAAMAAQQJAAIADgB4AAMAAQQJAAMAHAAyAAMAAQQJAAQAHACGAAMAAQQJAAUAFgAcAAMAAQQJAAYAHABcAAMAAQQJAAoAKACiAEYAaQBsAGUAIABBAHcAYQB5ACAASABlAGwAcABWAGUAcgBzAGkAbwBuACAAMAAuADAARgBpAGwAZQAgAEEAdwBhAHkAIABIAGUAbABwRmlsZSBBd2F5IEhlbHAARgBpAGwAZQAgAEEAdwBhAHkAIABIAGUAbABwAFIAZQBnAHUAbABhAHIARgBpAGwAZQAgAEEAdwBhAHkAIABIAGUAbABwAEcAZQBuAGUAcgBhAHQAZQBkACAAYgB5ACAASQBjAG8ATQBvAG8AbgADAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA) format('truetype'),
		 url(data:application/font-woff;charset=utf-8;base64,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) format('woff');
	font-weight: normal;
	font-style: normal;
}
[class*="fileaway-help-icon"] {
	font-family: 'File Away Help';
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	/* Better Font Rendering =========== */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
.fileaway-help-iconinfo:before {
	content: "\e600";
}
.fileaway-help-iconinfo2:before {
	content: "\e601";
}
.fileaway-help-iconclose:before {
	content: "\e602";
}
.fileaway-help-iconquestion:before {
	content: "\e603";
}
.fileaway-help-iconclose2:before {
	content: "\e604";
}
.fileaway-help-iconhelp:before {
	content: "\e605";
}
.fileaway-help-iconcross:before {
	content: "\e606";
}
.fileaway-help-iconinfo3:before {
	content: "\e607";
}
.fileaway-help-iconinfo4:before {
	content: "\e608";
}
.fileaway-help-iconquestion2:before {
	content: "\e609";
}
.chozed-container-single .chozed-single {
	background-image: none!important;
	height: 22px!important;
	padding: 0 0 0 5px!important;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	overflow: hidden;
	white-space: nowrap;
	position: relative;
	line-height: 23px;
	text-decoration: none;
	color: #444444;
	background: #f8f6f2!important; 
	background-color: #f8f6f2!important; 
	border-color: #dddddd;
	border-width: 1px;
	border-style: solid;
	-moz-border-radius: 0;
	-webkit-border-radius: 0;
	border-radius: 0;
	width: 100%;
	max-width: 100%;
	font-size: 12px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}	
.chozed-container-single .chozed-drop {
	-webkit-border-radius: 0 0 4px 4px;
	-moz-border-radius: 0 0 4px 4px;
	border-radius: 0 0 4px 4px;
	-moz-background-clip: padding;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
}
.chozed-container .chozed-drop {
	background: #f8f6f2!important; 
	border: 1px solid #aaa;
	border-top: 0;
	position: absolute;
	-webkit-box-shadow: 0 4px 5px rgba(0,0,0,.15);
	-moz-box-shadow: 0 4px 5px rgba(0,0,0,.15);
	box-shadow: 0 4px 5px rgba(0,0,0,.15);
	z-index: 1010;
}
.chozed-container-multi .chozed-choices li.search-field input[type="text"] {
	height:30px!important;
}
#fileawaymodal-form input[type="search"],
#fileawaymodal-form input[type="search"]:focus,
div.chozed-search > input,
div.chozed-search > input:focus,
div.chozed-search > input:active, 
{
	background-color: #FFFFFF!important
}
#fileawaymodal-form div.clearfix:after {
	visibility: hidden;
	display: block;
	font-size: 0;
	content: " ";
	clear: both;
	height: 0;
}
#fileawaymodal-form div.clearfix {display: inline-block;}