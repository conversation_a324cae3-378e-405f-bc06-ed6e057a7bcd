<?php
defined('fileaway') or die('Water, water everywhere, but not a drop to drink.');
$output .=  
'/* YOURCOLOR PRIMARY */
/* 
	Add any custom list classes here to hook them into your color, 
	adding a comma after each selector.
*/
h3.ssfa-yourcolor,
div.ssfa-flightbox-controls.ssfa-yourcolor span,
table[id^="ssfa-table"] tbody td div.ssfa-player.ssfa-yourcolor span[id^=fileaplay_], 
table[id^="ssfa-table"] tbody td div.ssfa-player.ssfa-yourcolor span[id^=fileaplay_]:hover,
table[id^="ssfa-table"] tbody td div.ssfa-player.ssfa-yourcolor span[id^=fileaplay_]:active, 
table[id^="ssfa-table"] tbody td span.ssfa-faminicon.ssfa-yourcolor,
table[id^="ssfa-table"] tbody td span.ssfa-faminicon.ssfa-yourcolor:hover,
table[id^="ssfa-table"] tbody td span.ssfa-faminicon.ssfa-yourcolor:active,
table[id^="ssfa-table"] tbody td span.ssfa-faminicon.ssfa-yourcolor,
table[id^="ssfa-table"] tbody td a.ssfa-yourcolor span.ssfa-filename,
table[id^="ssfa-table"] tbody td a.ssfa-yourcolor:visited span.ssfa-filename,
div[id^="ssfa-list-wrap"].ssfa-silk a.ssfa-yourcolor,
div[id^="ssfa-list-wrap"].ssfa-silk a.ssfa-yourcolor:visited,
div[id^="ssfa-list-wrap"].ssfa-minimal-list a.ssfa-yourcolor,
div[id^="ssfa-list-wrap"].ssfa-minimal-list a.ssfa-yourcolor:visited,
div[id^="ssfa-list-wrap"].ssfa-minimal-list a.ssfa-yourcolor:hover,
div[id^="ssfa-list-wrap"].ssfa-minimal-list a.ssfa-yourcolor:active,
div[id^="ssfa-list-wrap"].ssfa-silk span.ssfa-listicon.ssfa-yourcolor, 
div[id^="ssfa-list-wrap"].ssfa-silk span.ssfa-listicon.ssfa-yourcolor a, 
div[id^="ssfa-list-wrap"].ssfa-silk span.ssfa-listicon.ssfa-yourcolor a:hover, 
div[id^="ssfa-list-wrap"].ssfa-silk span.ssfa-listicon.ssfa-yourcolor a:active, 
div[id^="ssfa-list-wrap"].ssfa-silk span.ssfa-listicon.ssfa-yourcolor a:visited,
div[id^="ssfa-list-wrap"].ssfa-silk span.ssfa-paperclip.ssfa-yourcolor, 
div[id^="ssfa-list-wrap"].ssfa-silk span.ssfa-paperclip.ssfa-yourcolor a, 
div[id^="ssfa-list-wrap"].ssfa-silk span.ssfa-paperclip.ssfa-yourcolor a:hover, 
div[id^="ssfa-list-wrap"].ssfa-silk span.ssfa-paperclip.ssfa-yourcolor a:active, 
div[id^="ssfa-list-wrap"].ssfa-silk span.ssfa-paperclip.ssfa-yourcolor a:visited, 
div[id^="ssfa-list-wrap"].ssfa-minimal-list span.ssfa-listicon.ssfa-yourcolor, 
div[id^="ssfa-list-wrap"].ssfa-minimal-list span.ssfa-listicon.ssfa-yourcolor a, 
div[id^="ssfa-list-wrap"].ssfa-minimal-list span.ssfa-listicon.ssfa-yourcolor a:hover, 
div[id^="ssfa-list-wrap"].ssfa-minimal-list span.ssfa-listicon.ssfa-yourcolor a:active, 
div[id^="ssfa-list-wrap"].ssfa-minimal-list span.ssfa-listicon.ssfa-yourcolor a:visited,
div[id^="ssfa-list-wrap"].ssfa-minimal-list span.ssfa-paperclip.ssfa-yourcolor, 
div[id^="ssfa-list-wrap"].ssfa-minimal-list span.ssfa-paperclip.ssfa-yourcolor a, 
div[id^="ssfa-list-wrap"].ssfa-minimal-list span.ssfa-paperclip.ssfa-yourcolor a:hover, 
div[id^="ssfa-list-wrap"].ssfa-minimal-list span.ssfa-paperclip.ssfa-yourcolor a:active, 
div[id^="ssfa-list-wrap"].ssfa-minimal-list span.ssfa-paperclip.ssfa-yourcolor a:visited { 
	color: #YOURCOLOR; 
}
div.ssfa-up-progress-yourcolor {
	background: #YOURCOLOR;
}
d0iv.ssfa-flightbox-controls.ssfa-yourcolor .flightbox-spinner > div { 
	background-color: #YOURCOLOR; 
}
/* END YOURCOLOR PRIMARY */
';