﻿=== File Away ===
Contributors: thomstark
Donate link: https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=2JHFN4UF23ARG
Tags: files, attachments, upload, statistics, tables, directory, monetize, lightbox, audio, video, file manager, encryption
Requires at least: 3.7
Tested up to: 5.1.1
Requires PHP: 5.4
Stable tag: *******.1
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Upload, manage, and display files from your server directories or page attachments in stylized lists or sortable data tables.

== Description ==
Upload, manage, and display files from your server directories or page attachments in stylized lists or sortable data tables. And much, much more. 

== Changelog ==
= *******.1 =
* Fixed download error when stats and encryption are enabled, was caused by previous sanitization update 3.9.7.
= ******* =
* Bugfix for intermittent bug with filename encryption
= ******* =
* Added show_wp_thumbs attribute to [fileaway] shortcode. Omit for default behavior (hide). show_wp_thumbs="true" to show them.
* Modified bulk action select all behavior to only select files on the current page if the file table is paginated
= ******* =
* Fixed bug with location nonce verification for setups where Site URL and WP Url are different
= ******* =
* Fixed Flightbox navigation which was broken by an earlier update
* Removed deprecated encryption method
= ******* =
* Forgot to include a file update for Windows compat in the last update. This time it's for real.
* Made alt pathinfo method the only pathinfo. Should fix most issues with multibyte filenames (e.g., Chinese, Russian, etc.)
= ******* =
* Full compat with Windows/iis/xampp
* Fixed maxsize bug with fileup shortcode
= ******* =
* Important bugfix for timezone handling. 
* Improved error handling for ajax functions: more descriptive for troubleshooting purposes.
* Increased speed of animations in manager mode. 
= ******* =
* Added the parentlabel attribute to the fileaway shortcode, allowing you to specify a pseudonym for the topmost directory in a Directory Tree Nav or Manager Mode table
= ******* =
* Fixed bug with symlinks validator that only validated one subdirectory deep
= 3.9.8 =
* Added option to allow symlinks in file paths. Disabled by default. 
= ******* =
* Update to allow symlinks to escape new path validation checks unscathed
= ******* =
* Fixed bug for PHP < 5.5 which prevents plugin activation
= ******* =
* Fixed issue with new path validation's incompatibility with wp installs in a subdirectory at different domain than front-end site
= ******* =
* Fixed dynamic paths not working
= ******* =
* important patch
= ******* =
* prettify set to "off" now applies to directory names as well
* one bugfix
* additional security improvements
= ******* =
* Timezone handling improvements
* Additional validation checks added
= ******* =
* Added filename sanitizer
= ******* =
* Moved downloader class to WP action
* added comments to several sanitization and validation methods
= 3.9.7 =
* Important Security Patches
* PHP 7 compat
* Introduced Windows-friendly Stripslashes method
* A completely new edition of File Away is in the works. Stay tuned.

== Upgrade Notice ==
= ******* = 
Security patches and validation improvements. Update immediately.